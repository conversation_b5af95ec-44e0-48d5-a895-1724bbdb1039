#!/usr/bin/env python3
"""
Test script to validate the implementation of all requested changes:
1. Unused folders deletion
2. Temp Files folder setting in Settings tab
3. File creation refactoring to use Temp folder
4. FAQ and Guide documentation
5. Export button for image comparison
"""

import os
import sys
import json
from pathlib import Path

def test_unused_folders_deleted():
    """Test that unused folders were deleted"""
    print("Testing unused folders deletion...")
    
    # Check that test_cases and test_suites folders were removed from root
    root_test_cases = Path("test_cases")
    root_test_suites = Path("test_suites")
    
    if root_test_cases.exists():
        print("❌ FAIL: test_cases folder still exists in root")
        return False
    
    if root_test_suites.exists():
        print("❌ FAIL: test_suites folder still exists in root")
        return False
    
    print("✅ PASS: Unused folders successfully deleted")
    return True

def test_temp_directory_configuration():
    """Test that temp directory configuration is working"""
    print("Testing temp directory configuration...")
    
    # Check if temp directory exists
    temp_dir = Path("temp")
    if not temp_dir.exists():
        print("❌ FAIL: temp directory does not exist")
        return False
    
    # Check if temp directory is configured in config files
    config_files = ["config.py", "config_android.py"]
    for config_file in config_files:
        if Path(config_file).exists():
            with open(config_file, 'r') as f:
                content = f.read()
                if 'TEMP_FILES' not in content:
                    print(f"❌ FAIL: TEMP_FILES not found in {config_file}")
                    return False
    
    print("✅ PASS: Temp directory configuration working")
    return True

def test_file_utils_temp_functions():
    """Test that file utils temp functions are implemented"""
    print("Testing file utils temp functions...")
    
    try:
        # Test iOS version
        sys.path.append('app')
        from utils.file_utils import get_temp_directory, get_temp_subdirectory
        
        temp_dir = get_temp_directory()
        if not temp_dir:
            print("❌ FAIL: get_temp_directory() returned None")
            return False
        
        # Test creating a subdirectory
        test_subdir = get_temp_subdirectory('test_validation')
        if not os.path.exists(test_subdir):
            print("❌ FAIL: get_temp_subdirectory() did not create directory")
            return False
        
        # Clean up test directory
        os.rmdir(test_subdir)
        
        print("✅ PASS: File utils temp functions working")
        return True
        
    except ImportError as e:
        print(f"❌ FAIL: Could not import temp functions: {e}")
        return False
    except Exception as e:
        print(f"❌ FAIL: Error testing temp functions: {e}")
        return False

def test_settings_ui_changes():
    """Test that Settings UI includes temp files configuration"""
    print("Testing Settings UI changes...")
    
    template_files = ["app/templates/index.html", "app_android/templates/index.html"]
    
    for template_file in template_files:
        if Path(template_file).exists():
            with open(template_file, 'r') as f:
                content = f.read()
                
                # Check for temp files directory input
                if 'tempFilesDir' not in content:
                    print(f"❌ FAIL: tempFilesDir not found in {template_file}")
                    return False
                
                # Check for clear temp files button
                if 'clearTempFilesBtn' not in content:
                    print(f"❌ FAIL: clearTempFilesBtn not found in {template_file}")
                    return False
    
    print("✅ PASS: Settings UI changes implemented")
    return True

def test_guide_documentation():
    """Test that Guide documentation is implemented"""
    print("Testing Guide documentation...")
    
    # Check if guide HTML file exists
    guide_files = [
        "app/static/guide/faq_guide.html",
        "app_android/static/guide/faq_guide.html"
    ]
    
    for guide_file in guide_files:
        if not Path(guide_file).exists():
            print(f"❌ FAIL: Guide file not found: {guide_file}")
            return False
        
        # Check if guide file has content
        with open(guide_file, 'r') as f:
            content = f.read()
            if len(content) < 1000:  # Should be substantial content
                print(f"❌ FAIL: Guide file too small: {guide_file}")
                return False
    
    # Check if Guide tab is added to templates
    template_files = ["app/templates/index.html", "app_android/templates/index.html"]
    
    for template_file in template_files:
        if Path(template_file).exists():
            with open(template_file, 'r') as f:
                content = f.read()
                
                if 'guide-tab-btn' not in content:
                    print(f"❌ FAIL: Guide tab not found in {template_file}")
                    return False
    
    print("✅ PASS: Guide documentation implemented")
    return True

def test_export_button_implementation():
    """Test that Export button is implemented"""
    print("Testing Export button implementation...")
    
    # Check if Export button is added to tools templates
    tools_files = ["app/templates/tools.html", "app_android/templates/tools.html"]
    
    for tools_file in tools_files:
        if Path(tools_file).exists():
            with open(tools_file, 'r') as f:
                content = f.read()
                
                if 'exportComparisonBtn' not in content:
                    print(f"❌ FAIL: Export button not found in {tools_file}")
                    return False
                
                if 'Export' not in content:
                    print(f"❌ FAIL: Export button text not found in {tools_file}")
                    return False
    
    # Check if export endpoints are added to routes
    routes_files = ["app/routes/tools.py", "app_android/routes/tools.py"]
    
    for routes_file in routes_files:
        if Path(routes_file).exists():
            with open(routes_file, 'r') as f:
                content = f.read()
                
                if 'export-comparison' not in content:
                    print(f"❌ FAIL: Export endpoint not found in {routes_file}")
                    return False
                
                if 'download-export' not in content:
                    print(f"❌ FAIL: Download endpoint not found in {routes_file}")
                    return False
    
    print("✅ PASS: Export button implementation complete")
    return True

def main():
    """Run all tests"""
    print("🧪 Running implementation validation tests...\n")
    
    tests = [
        test_unused_folders_deleted,
        test_temp_directory_configuration,
        test_file_utils_temp_functions,
        test_settings_ui_changes,
        test_guide_documentation,
        test_export_button_implementation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ FAIL: Test {test.__name__} crashed: {e}\n")
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Implementation is complete and working.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
