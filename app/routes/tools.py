import os
import glob
import subprocess
import threading
import time
import logging
from pathlib import Path
from flask import Blueprint, request, jsonify
import sqlite3

logger = logging.getLogger(__name__)

tools_bp = Blueprint('tools', __name__, url_prefix='/api/tools')

# Global variable to track sqlite-web processes
sqlite_web_processes = {}

@tools_bp.route('/scan-databases', methods=['GET'])
def scan_databases():
    """Scan for SQLite database files in the app directory"""
    try:
        # Get the app directory
        app_dir = Path(__file__).resolve().parent.parent
        
        # Common database file patterns
        db_patterns = [
            '**/*.db',
            '**/*.sqlite',
            '**/*.sqlite3'
        ]
        
        databases = []
        
        for pattern in db_patterns:
            for db_path in app_dir.glob(pattern):
                if db_path.is_file():
                    try:
                        # Get file size
                        size = db_path.stat().st_size
                        size_str = format_file_size(size)
                        
                        # Verify it's actually a SQLite database
                        if is_sqlite_database(str(db_path)):
                            databases.append({
                                'name': db_path.name,
                                'path': str(db_path),
                                'relative_path': str(db_path.relative_to(app_dir)),
                                'size': size_str
                            })
                    except Exception as e:
                        logger.warning(f"Error processing database file {db_path}: {e}")
        
        # Sort by name
        databases.sort(key=lambda x: x['name'])
        
        return jsonify({
            'success': True,
            'databases': databases,
            'count': len(databases)
        })
        
    except Exception as e:
        logger.error(f"Error scanning databases: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@tools_bp.route('/open-database', methods=['POST'])
def open_database():
    """Open a SQLite database using sqlite-web"""
    try:
        data = request.get_json()
        database_path = data.get('database_path')
        
        if not database_path:
            return jsonify({
                'success': False,
                'error': 'Database path is required'
            }), 400
        
        if not os.path.exists(database_path):
            return jsonify({
                'success': False,
                'error': 'Database file does not exist'
            }), 404
        
        # Find an available port starting from 8090
        port = find_available_port(8090)
        if not port:
            return jsonify({
                'success': False,
                'error': 'No available ports found'
            }), 500
        
        # Start sqlite-web process
        try:
            # Kill any existing process for this database
            if database_path in sqlite_web_processes:
                try:
                    sqlite_web_processes[database_path].terminate()
                    sqlite_web_processes[database_path].wait(timeout=5)
                except:
                    pass

            # Find sqlite-web executable - check virtual environment first
            sqlite_web_cmd = 'sqlite_web'  # Note: executable is sqlite_web not sqlite-web

            # Check if we're in a virtual environment
            venv_path = os.environ.get('VIRTUAL_ENV')

            if venv_path:
                venv_sqlite_web = os.path.join(venv_path, 'bin', 'sqlite_web')
                if os.path.exists(venv_sqlite_web):
                    sqlite_web_cmd = venv_sqlite_web
            else:
                # Try to find venv relative to the app directory
                app_dir = Path(__file__).resolve().parent.parent.parent
                venv_sqlite_web = app_dir / 'venv' / 'bin' / 'sqlite_web'
                if venv_sqlite_web.exists():
                    sqlite_web_cmd = str(venv_sqlite_web)

            # Start new sqlite-web process
            cmd = [sqlite_web_cmd, database_path, '--host', '127.0.0.1', '--port', str(port)]
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Store the process
            sqlite_web_processes[database_path] = process
            
            # Give it a moment to start
            time.sleep(2)
            
            # Check if process is still running
            if process.poll() is None:
                url = f"http://127.0.0.1:{port}"
                return jsonify({
                    'success': True,
                    'url': url,
                    'port': port,
                    'database': os.path.basename(database_path)
                })
            else:
                # Process failed to start
                stdout, stderr = process.communicate()
                return jsonify({
                    'success': False,
                    'error': f'Failed to start sqlite-web: {stderr}'
                }), 500
                
        except FileNotFoundError as e:
            logger.error(f"sqlite-web not found: {e}")
            return jsonify({
                'success': False,
                'error': 'sqlite-web is not installed. Please install it using: pip install sqlite-web'
            }), 500
        except Exception as e:
            logger.error(f"Error starting sqlite-web: {e}")
            logger.error(f"Command attempted: {cmd}")
            return jsonify({
                'success': False,
                'error': f'Error starting sqlite-web: {str(e)}'
            }), 500
        
    except Exception as e:
        logger.error(f"Error opening database: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@tools_bp.route('/validate-folder', methods=['POST'])
def validate_folder():
    """Validate if a folder exists and count files in it"""
    try:
        data = request.get_json()
        folder_path = data.get('folder_path')
        
        if not folder_path:
            return jsonify({
                'success': False,
                'error': 'Folder path is required'
            }), 400
        
        folder_path = Path(folder_path)
        
        if not folder_path.exists():
            return jsonify({
                'success': True,
                'exists': False,
                'file_count': 0
            })
        
        if not folder_path.is_dir():
            return jsonify({
                'success': False,
                'error': 'Path exists but is not a directory'
            }), 400
        
        # Count image files
        image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff']
        file_count = 0
        
        for ext in image_extensions:
            file_count += len(list(folder_path.glob(f'*{ext}')))
            file_count += len(list(folder_path.glob(f'*{ext.upper()}')))
        
        return jsonify({
            'success': True,
            'exists': True,
            'file_count': file_count
        })
        
    except Exception as e:
        logger.error(f"Error validating folder: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@tools_bp.route('/compare-images', methods=['POST'])
def compare_images():
    """Generate image comparison report"""
    try:
        data = request.get_json()
        baseline_folder = data.get('baseline_folder')
        new_folder = data.get('new_folder')
        
        if not baseline_folder or not new_folder:
            return jsonify({
                'success': False,
                'error': 'Both baseline_folder and new_folder are required'
            }), 400
        
        baseline_path = Path(baseline_folder)
        new_path = Path(new_folder)
        
        if not baseline_path.exists() or not baseline_path.is_dir():
            return jsonify({
                'success': False,
                'error': 'Baseline folder does not exist or is not a directory'
            }), 400
        
        if not new_path.exists() or not new_path.is_dir():
            return jsonify({
                'success': False,
                'error': 'New folder does not exist or is not a directory'
            }), 400
        
        # Get the image comparison script path
        app_dir = Path(__file__).resolve().parent.parent.parent
        comparison_script = app_dir / 'image_comparison' / 'generate_html_report.py'
        
        if not comparison_script.exists():
            return jsonify({
                'success': False,
                'error': 'Image comparison script not found'
            }), 500
        
        # Create output directory in temp folder
        from utils.file_utils import get_temp_subdirectory
        output_dir = Path(get_temp_subdirectory('image_comparison')) / 'report'
        output_dir.mkdir(exist_ok=True)
        
        # Run the comparison script
        try:
            cmd = [
                'python',
                str(comparison_script),
                '--baseline', str(baseline_path),
                '--new', str(new_path),
                '--output', str(output_dir)
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                # Find the generated report
                report_file = output_dir / 'report.html'
                if report_file.exists():
                    # Start a simple HTTP server to serve the report
                    port = find_available_port(8100)
                    if port:
                        # Start server in background
                        start_report_server(str(output_dir), port)
                        url = f"http://127.0.0.1:{port}/report.html"
                        
                        return jsonify({
                            'success': True,
                            'report_url': url,
                            'port': port
                        })
                    else:
                        return jsonify({
                            'success': False,
                            'error': 'No available ports for report server'
                        }), 500
                else:
                    return jsonify({
                        'success': False,
                        'error': 'Report file was not generated'
                    }), 500
            else:
                return jsonify({
                    'success': False,
                    'error': f'Image comparison failed: {result.stderr}'
                }), 500
                
        except subprocess.TimeoutExpired:
            return jsonify({
                'success': False,
                'error': 'Image comparison timed out'
            }), 500
        except Exception as e:
            return jsonify({
                'success': False,
                'error': f'Error running image comparison: {str(e)}'
            }), 500
        
    except Exception as e:
        logger.error(f"Error comparing images: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# Helper functions
def format_file_size(size_bytes):
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def is_sqlite_database(file_path):
    """Check if a file is a valid SQLite database"""
    try:
        conn = sqlite3.connect(file_path)
        conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
        conn.close()
        return True
    except:
        return False

def find_available_port(start_port):
    """Find an available port starting from start_port"""
    import socket
    
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return port
        except OSError:
            continue
    return None

def start_report_server(directory, port):
    """Start a simple HTTP server to serve the report"""
    import http.server
    import socketserver
    import threading
    
    def run_server():
        os.chdir(directory)
        handler = http.server.SimpleHTTPRequestHandler
        with socketserver.TCPServer(("127.0.0.1", port), handler) as httpd:
            httpd.serve_forever()
    
    thread = threading.Thread(target=run_server, daemon=True)
    thread.start()
